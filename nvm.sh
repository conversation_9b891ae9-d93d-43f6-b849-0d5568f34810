#!/bin/bash

if [[ ! -d  ~/.nvm ]]; then
  # Get the latest NVM version from GitHub API
  LATEST_NVM_VERSION=$(curl -s https://api.github.com/repos/nvm-sh/nvm/releases/latest | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/')
  echo "Installing latest NVM version: $LATEST_NVM_VERSION"

  # Install the latest version of NVM
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/${LATEST_NVM_VERSION}/install.sh | bash
  export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

  # Install Node.js LTS
  echo "Installing Node.js LTS..."
  nvm install --lts
  nvm use --lts

  # Install pnpm globally
  echo "Installing pnpm package manager..."
  npm install -g pnpm

  echo "Node.js and pnpm installation completed!"
else
  # NVM already exists, but check if pnpm is installed
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm

  if ! command -v pnpm &> /dev/null; then
    echo "NVM already installed, but pnpm not found. Installing pnpm..."
    npm install -g pnpm
    echo "pnpm installation completed!"
  else
    echo "NVM and pnpm are already installed!"
  fi
fi
