clear && fastfetch
if [[ -r "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-instant-prompt-${(%):-%n}.zsh" ]]; then
  source "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-instant-prompt-${(%):-%n}.zsh"
fi
# Enable Powerlevel10k instant prompt. Should stay close to the top of ~/.zshrc.
# Initialization code that may require console input (password prompts, [y/n]
# confirmations, etc.) must go above this block; everything else may go below.


# If you come from bash you might have to change your $PATH.
# export PATH=$HOME/bin:$HOME/.local/bin:/usr/local/bin:$PATH

# Path to your Oh My Zsh installation.
export ZSH="$HOME/.oh-my-zsh"

# Set name of the theme to load --- if set to "random", it will
# load a random theme each time Oh My Zsh is loaded, in which case,
# to know which specific one was loaded, run: echo $RANDOM_THEME
# See https://github.com/ohmyzsh/ohmyzsh/wiki/Themes
ZSH_THEME="powerlevel10k/powerlevel10k"

# Set list of themes to pick from when loading at random
# Setting this variable when ZSH_THEME=random will cause zsh to load
# a theme from this variable instead of looking in $ZSH/themes/
# If set to an empty array, this variable will have no effect.
# ZSH_THEME_RANDOM_CANDIDATES=( "robbyrussell" "agnoster" )

# Uncomment the following line to use case-sensitive completion.
# CASE_SENSITIVE="true"

# Uncomment the following line to use hyphen-insensitive completion.
# Case-sensitive completion must be off. _ and - will be interchangeable.
# HYPHEN_INSENSITIVE="true"

# Uncomment one of the following lines to change the auto-update behavior
# zstyle ':omz:update' mode disabled  # disable automatic updates
# zstyle ':omz:update' mode auto      # update automatically without asking
# zstyle ':omz:update' mode reminder  # just remind me to update when it's time

# Uncomment the following line to change how often to auto-update (in days).
# zstyle ':omz:update' frequency 13

# Uncomment the following line if pasting URLs and other text is messed up.
# DISABLE_MAGIC_FUNCTIONS="true"

# Uncomment the following line to disable colors in ls.
# DISABLE_LS_COLORS="true"

# Uncomment the following line to disable auto-setting terminal title.
# DISABLE_AUTO_TITLE="true"

# Uncomment the following line to enable command auto-correction.
# ENABLE_CORRECTION="true"

# Uncomment the following line to display red dots whilst waiting for completion.
# You can also set it to another string to have that shown instead of the default red dots.
# e.g. COMPLETION_WAITING_DOTS="%F{yellow}waiting...%f"
# Caution: this setting can cause issues with multiline prompts in zsh < 5.7.1 (see #5765)
# COMPLETION_WAITING_DOTS="true"

# Uncomment the following line if you want to disable marking untracked files
# under VCS as dirty. This makes repository status check for large repositories
# much, much faster.
# DISABLE_UNTRACKED_FILES_DIRTY="true"

# Uncomment the following line if you want to change the command execution time
# stamp shown in the history command output.
# You can set one of the optional three formats:
# "mm/dd/yyyy"|"dd.mm.yyyy"|"yyyy-mm-dd"
# or set a custom format using the strftime function format specifications,
# see 'man strftime' for details.
# HIST_STAMPS="mm/dd/yyyy"

# Would you like to use another custom folder than $ZSH/custom?
# ZSH_CUSTOM=/path/to/new-custom-folder

# Which plugins would you like to load?
# Standard plugins can be found in $ZSH/plugins/
# Custom plugins may be added to $ZSH_CUSTOM/plugins/
# Example format: plugins=(rails git textmate ruby lighthouse)
# Add wisely, as too many plugins slow down shell startup.
plugins=(git)
plugins+=(zsh-nvm zsh-syntax-highlighting zsh-autosuggestions)

source $ZSH/oh-my-zsh.sh

# User configuration
alias awsconf='code ~/.aws'
alias b64up='docker run -d --name b64 -p 3000:3000 qwrobins/b64:v1.0.6'
alias b64down='docker rm -f b64'
alias argld="kubectl delete pod -l app.kubernetes.io/name=argocd-dex-server -n argocd && kubectl delete pod -l app.kubernetes.io/name=argocd-server -n argocd"
alias cc='claude --dangerously-skip-permissions'
alias k='kubectl'
alias kaf='kubectl apply -f'
alias kcf='kubectl create -f'
alias kdf='kubectl delete -f'
alias kak='kubectl apply -k'
alias kdk='kubectl delete -k'
alias kndstart='docker start $(docker ps -aq --filter name=kind)'
alias kndstop='docker ps -q --filter "name=kind" | xargs -r docker stop'
alias k9rst='rm ~/.config/k9s/config.yml'
alias kxclr='rm -Rf ~/.kube/config'
alias trf='terraform'
alias trfaa='terraform apply --auto-approve'
alias trfda='terraform destroy --auto-approve'
alias trg='/usr/bin/terragrunt'
alias tag='/usr/bin/terragrunt --auto-approve'
alias apb='ansible-playbook'
alias gpush="git pull &&git add -A && git commit -am 'update' && git push"
alias ezsh='code ~/.zshrc'
alias czsh='cursor ~/.zshrc'
alias mkube='$HOME/development/minikube/mkube-start.sh'
alias mkube3='minikube start --kubernetes-version=1.28.0 --cpus=4 --memory=8192 --nodes=3 --cni=cilium --addons=dashboard,metrics-server'
alias ags='kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d; echo'
alias vgs='kubectl -n vault get secret vault-unseal-keys -o jsonpath="{.data.vault-root}" | base64 -d; echo'
alias agpf='kubectl port-forward svc/argocd-server -n argocd 8080:443'
alias pgpf='kubectl port-forward svc/qwrnet-pg-cluster-rw -n postgres 5432:5432'
alias acdqr='argocd account update-password --account qwrobins --new-password P@sswd12'
alias mkstop='minikube stop'
alias mkda='minikube delete --all'
alias yca='yarn create react-app'
alias twrct='yarn add tailwindcss postcss autoprefixer && npx tailwindcss init -p'
alias tsrld='sudo tailscale down && sudo tailscale up --accept-routes'
alias tsdown='sudo tailscale down'
alias tsup='sudo tailscale up --accept-routes'
alias rld='source ~/.zshrc'
alias mrsc='sudo mount -t cifs -o username=qadm,password=P@sswd12,uid=1000,gid=1000,file_mode=0777,dir_mode=0777 //rsc.qwrobins.net/data /mnt/data'
alias kctx='kubectx'
alias python='python3'
alias pbcopy='xclip -selection clipboard'
alias pbpaste='xclip -selection clipboard -o'
alias cdvbak='cd /media/qwrobins/V4T/backups/g950/qwrobins'
alias dcud='docker-compose up -d'
alias dcdown='docker-compose down'
alias pgup='docker run -d --rm --name pg -p 5432:5432 -e POSTGRES_PASSWORD=password postgres:16'
alias pgdown='docker stop pg'
alias hbrstart='cd ~/development/harbor && sudo docker-compose up -d'
alias hbrstop='cd ~/development/harbor && sudo docker-compose down'
alias ls='eza --icons --git -a'
alias yay-sys='PATH=/usr/bin:/usr/local/bin:$PATH yay'
alias mdbox='rclone mount qr-dropbox:qr-dropbox ~/qr-dropbox --daemon'
alias umdbox='fusermount -u ~/qr-dropbox'

########### LP AWS Profiles ###########
alias lp='aws-vault --debug exec lp --duration=1h --no-session $SHELL'
alias qrdev='aws-vault --debug exec qr-dev --duration=8h --no-session $SHELL'
alias qa='aws-vault --debug exec lp-qa --duration=4h --no-session $SHELL'
alias qaukc='aws eks update-kubeconfig --name sandbox-qa-qa --alias qa --region us-east-1'
alias stgukc='aws eks update-kubeconfig --name staging_eks1 --alias staging --region us-east-1'
alias ops='aws-vault --debug exec ops --duration=1h --no-session $SHELL'
alias staging='aws-vault --debug exec lp-staging --duration=4h --no-session $SHELL'
alias dev='aws-vault --debug exec dev-sandbox --duration=6h --no-session $SHELL'
alias sboxukc='aws eks update-kubeconfig --name sandbox-infra-operation --alias sbox --region us-east-1'
alias lpukc='aws eks update-kubeconfig --name lp-cluster --alias lp-cluster --region us-east-1'
alias lp1ukc='aws eks update-kubeconfig --name production_eks1 --alias production_eks1 --region us-east-1'
alias sdevukc='aws eks update-kubeconfig --name swat-dev --alias swat-dev --region us-east-2'
alias qrdevukc='aws eks update-kubeconfig --name qr-sandbox-dev --alias qr-dev --region us-east-2'
alias lpcluster='kubectx arn:aws:eks:us-east-1:381475384502:cluster/lp-cluster'
alias sboxkc='kubectx arn:aws:eks:us-east-1:172158540696:cluster/sandbox-infra-operation'
alias swdevkc='kubectx arn:aws:eks:us-east-2:825569692836:cluster/swat-dev'
alias vlogin='vault login token=$VAULT_TOKEN'
alias lpdevukc='aws eks update-kubeconfig --name lp-dev --alias swat-dev --region us-east-1'

########### Cursor with AWS Profiles ###########
alias crslp='aws-vault exec lp --duration=1h --no-session -- cursor'
alias kawsmcp='docker stop $(docker ps -a -q --filter ancestor=ghcr.io/alexei-led/aws-mcp-server:latest) && docker rm $(docker ps -a -q --filter ancestor=ghcr.io/alexei-led/aws-mcp-server:latest)'

export XDG_CONFIG_HOME="$HOME/.config"
export XDG_DATA_HOME="$HOME/.local/share"
export XDG_CACHE_HOME="$HOME/.cache"
export GO111MODULE=on
export GOPATH=$HOME/go
export GITHUB_TOKEN="****************************************"
export PATH=$PATH:$GOPATH/bin
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
export EDITOR="code --wait"
export PATH=$PATH:$HOME/.bin:$HOME/.local/bin
source /home/<USER>/.cargo/env

export AWS_VAULT_BACKEND=file

# export MANPATH="/usr/local/man:$MANPATH"

# You may need to manually set your language environment
# export LANG=en_US.UTF-8

# Preferred editor for local and remote sessions
# if [[ -n $SSH_CONNECTION ]]; then
#   export EDITOR='vim'
# else
#   export EDITOR='nvim'
# fi

# Compilation flags
# export ARCHFLAGS="-arch $(uname -m)"

# Set personal aliases, overriding those provided by Oh My Zsh libs,
# plugins, and themes. Aliases can be placed here, though Oh My Zsh
# users are encouraged to define aliases within a top-level file in
# the $ZSH_CUSTOM folder, with .zsh extension. Examples:
# - $ZSH_CUSTOM/aliases.zsh
# - $ZSH_CUSTOM/macos.zsh
# For a full list of active aliases, run `alias`.
#
# Example aliases
# alias zshconfig="mate ~/.zshrc"
# alias ohmyzsh="mate ~/.oh-my-zsh"

# >>> conda initialize >>>
# !! Contents within this block are managed by 'conda init' !!
__conda_setup="$('/home/<USER>/anaconda3/bin/conda' 'shell.zsh' 'hook' 2> /dev/null)"
if [ $? -eq 0 ]; then
    eval "$__conda_setup"
else
    if [ -f "/home/<USER>/anaconda3/etc/profile.d/conda.sh" ]; then
        . "/home/<USER>/anaconda3/etc/profile.d/conda.sh"
    else
        export PATH="/home/<USER>/anaconda3/bin:$PATH"
    fi
fi
unset __conda_setup
# <<< conda initialize <<<
# To customize prompt, run `p10k configure` or edit ~/.p10k.zsh.
[[ ! -f ~/.p10k.zsh ]] || source ~/.p10k.zsh

# pnpm
export PNPM_HOME="/home/<USER>/.local/share/pnpm"
case ":$PATH:" in
  *":$PNPM_HOME:"*) ;;
  *) export PATH="$PNPM_HOME:$PATH" ;;
esac
# pnpm end

# Task Master aliases added on 4/15/2025
alias tm='task-master'
alias taskmaster='task-master'
alias datagrip='datagrip-fixed'
